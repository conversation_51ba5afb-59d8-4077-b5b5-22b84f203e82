@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="box_shadow_wrapper">
                    @include('dashboard.admin.cms.include.navbar')
                </div>
            </div>

            <form action="{{ route('cms.privacy-terms.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="type" value="term" />
                <div class="form-card frst-step">
                    <div class="container">
                        <div class="row my-5">
                            <div class="col-md-12 mb-5">
                                <h3 class="section-heading mb-4"
                                    style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">Terms and Conditions</h3>
                            </div>

                            <div class="col-md-12 mb-5">
                                <div class="d-flex justify-content-end mb-3">
                                    <button type="button" class="btn btn-sm add-btn">Add Block</button>
                                </div>
                                @foreach ($terms as $index => $term)
                                    <div class="section-wrapper p-4 mb-4"
                                        style="border: 1px solid #ddd; border-radius: 8px; background-color: #f9f9f9;">
                                        <div class="d-flex justify-content-between align-items-center mb-4">
                                            <h4 class="mb-0" style="color: #555; font-weight: 600;">Section # {{ $index + 1 }}</h4>
                                            @if (!$loop->first)
                                                <button type="button" class="btn btn-danger btn-sm delete-block-btn">Delete this
                                                    block</button>
                                            @endif
                                        </div>

                                        <div class="mb-4">
                                            <label for="title" class="fieldlabels w-100">Title</label>
                                            <input type="text" class="form-control"
                                                name="term[{{ $index }}][title]"
                                                placeholder="Enter privacy terms title" value="{{ $term->title ?? '' }}" />
                                        </div>

                                        <div class="mb-4">
                                            <label for="description" class="fieldlabels w-100">Content</label>
                                            <textarea class="form-control" name="term[{{ $index }}][description]" rows="10"
                                                placeholder="Enter privacy terms content">{{ $term->description ?? '' }}</textarea>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="col-md-12 mt-4">
                                <button type="submit" class="btn btn-primary">Update</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
@push('js')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    $(document).on('click', '.add-btn', function() {
        const section = $('.section-wrapper').length;

        const newSection = `
            <div class="section-wrapper p-4 mb-4" style="border: 1px solid #ddd; border-radius: 8px; background-color: #f9f9f9;">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0" style="color: #555; font-weight: 600;">Section # ${section+1}</h4>
                    <button type="button" class="btn btn-danger btn-sm delete-block-btn">Delete this block</button>
                </div>

                <div class="mb-4">
                    <label class="fieldlabels w-100">Title</label>
                    <input type="text" class="form-control" name="term[${section}][title]" placeholder="Enter privacy policy title" />
                </div>

                <div class="mb-4">
                    <label class="fieldlabels w-100">Content</label>
                    <textarea class="form-control" name="term[${section}][description]" rows="10" placeholder="Enter privacy policy content"></textarea>
                </div>
            </div>
        `;

        $('.section-wrapper:last').after(newSection);
    });

    // Delete Block functionality
    $(document).on('click', '.delete-block-btn', function() {
        const totalSections = $('.section-wrapper').length;

        if (totalSections > 1) {
            $(this).closest('.section-wrapper').remove();
            updateSectionNumbers();
        } else {
            alert('You must have at least one section.');
        }
    });

    // Update section numbers after deletion
    function updateSectionNumbers() {
        $('.section-wrapper').each(function(index) {
            const sectionNumber = index + 1;
            $(this).find('h4').text('Section # ' + sectionNumber);
            $(this).find('input[name*="[title]"]').attr('name', `privacy[${index}][title]`);
            $(this).find('textarea[name*="[description]"]').attr('name', `privacy[${index}][description]`);
        });
    }
});
</script>
@endpush
