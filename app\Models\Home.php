<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Home extends Model
{
    use HasFactory;
    protected $fillable = [
        'banner_image',
        'kicker',
        'heading_one',
        'heading_two',
        'description',
        'image',
        'section_two_title',
        'section_two_image',
        'section_two_heading',
        'section_two_description',
        'section_three_title',
        'section_three_bg_image',
        'section_three_heading',
        'section_three_button_one_text',
        'section_three_button_one_link',
        'section_three_button_two_text',
        'section_three_button_two_link',
    ];
    
    public function details()
    {
        return $this->hasMany(HomeDetail::class);
    }
}
