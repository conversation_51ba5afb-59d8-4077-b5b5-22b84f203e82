<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="box_shadow_wrapper">
                    <?php echo $__env->make('dashboard.admin.cms.include.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            </div>

            <form action="#!" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="form-card frst-step">
                    <div class="container">
                        <div class="row my-5">
                            <div class="col-md-12 mb-5">
                                <h3 class="section-heading mb-4" style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">Hero Section</h3>
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="banner_image" class="fieldlabels w-100">Banner Image</label>
                                <input type="file" class="form-control" name="banner_image" id="banner_image" accept="image/*" />
                                <?php if($page && $page->banner_image): ?>
                                    <small class="text-muted">Current: <?php echo e($page->banner_image); ?></small>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="kicker" class="fieldlabels w-100">Kicker</label>
                                <input type="text" class="form-control" name="kicker" id="kicker" placeholder="Enter kicker text" value="<?php echo e($page ? $page->kicker : ''); ?>" />
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="heading_one" class="fieldlabels w-100">Heading One</label>
                                <input type="text" class="form-control" name="heading_one" id="heading_one" placeholder="Enter first heading" value="<?php echo e($page ? $page->heading_one : ''); ?>" />
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="heading_two" class="fieldlabels w-100">Heading Two</label>
                                <input type="text" class="form-control" name="heading_two" id="heading_two" placeholder="Enter second heading" value="<?php echo e($page ? $page->heading_two : ''); ?>" />
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="description" class="fieldlabels w-100">Description</label>
                                <textarea class="form-control" name="description" id="description" rows="4" placeholder="Enter description"><?php echo e($page ? $page->description : ''); ?></textarea>
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="image" class="fieldlabels w-100">Image</label>
                                <input type="file" class="form-control" name="image" id="image" accept="image/*" />
                                <?php if($page && $page->image): ?>
                                    <small class="text-muted">Current: <?php echo e($page->image); ?></small>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-12 mb-5 mt-5">
                                <h3 class="section-heading mb-4" style="color: #333; border-bottom: 2px solid #28a745; padding-bottom: 10px;">Section Two</h3>
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="section_two_title" class="fieldlabels w-100">Section Two Title</label>
                                <input type="text" class="form-control" name="section_two_title" id="section_two_title" placeholder="Enter section two title" value="<?php echo e($page ? $page->section_two_title : ''); ?>" />
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="section_two_image" class="fieldlabels w-100">Section Two Image</label>
                                <input type="file" class="form-control" name="section_two_image" id="section_two_image" accept="image/*" />
                                <?php if($page && $page->section_two_image): ?>
                                    <small class="text-muted">Current: <?php echo e($page->section_two_image); ?></small>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="section_two_heading" class="fieldlabels w-100">Section Two Heading</label>
                                <input type="text" class="form-control" name="section_two_heading" id="section_two_heading" placeholder="Enter section two heading" value="<?php echo e($page ? $page->section_two_heading : ''); ?>" />
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="section_two_description" class="fieldlabels w-100">Section Two Description</label>
                                <textarea class="form-control" name="section_two_description" id="section_two_description" rows="4" placeholder="Enter section two description"><?php echo e($page ? $page->section_two_description : ''); ?></textarea>
                            </div>

                            <div class="col-md-12 mb-5 mt-5">
                                <h3 class="section-heading mb-4" style="color: #333; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">Section Three</h3>
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="section_three_title" class="fieldlabels w-100">Section Three Title</label>
                                <input type="text" class="form-control" name="section_three_title" id="section_three_title" placeholder="Enter section three title" value="<?php echo e($page ? $page->section_three_title : ''); ?>" />
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="section_three_bg_image" class="fieldlabels w-100">Section Three Background Image</label>
                                <input type="file" class="form-control" name="section_three_bg_image" id="section_three_bg_image" accept="image/*" />
                                <?php if($page && $page->section_three_bg_image): ?>
                                    <small class="text-muted">Current: <?php echo e($page->section_three_bg_image); ?></small>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="section_three_heading" class="fieldlabels w-100">Section Three Heading</label>
                                <input type="text" class="form-control" name="section_three_heading" id="section_three_heading" placeholder="Enter section three heading" value="<?php echo e($page ? $page->section_three_heading : ''); ?>" />
                            </div>

                            <div class="col-md-12 mb-4">
                                <h5 class="sub-section-heading mb-3" style="color: #666; font-weight: 600;">Call-to-Action Buttons</h5>
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="section_three_button_one_text" class="fieldlabels w-100">Button One Text</label>
                                <input type="text" class="form-control" name="section_three_button_one_text" id="section_three_button_one_text" placeholder="Enter button one text" value="<?php echo e($page ? $page->section_three_button_one_text : ''); ?>" />
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="section_three_button_one_link" class="fieldlabels w-100">Button One Link</label>
                                <input type="url" class="form-control" name="section_three_button_one_link" id="section_three_button_one_link" placeholder="Enter button one link" value="<?php echo e($page ? $page->section_three_button_one_link : ''); ?>" />
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="section_three_button_two_text" class="fieldlabels w-100">Button Two Text</label>
                                <input type="text" class="form-control" name="section_three_button_two_text" id="section_three_button_two_text" placeholder="Enter button two text" value="<?php echo e($page ? $page->section_three_button_two_text : ''); ?>" />
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="section_three_button_two_link" class="fieldlabels w-100">Button Two Link</label>
                                <input type="url" class="form-control" name="section_three_button_two_link" id="section_three_button_two_link" placeholder="Enter button two link" value="<?php echo e($page ? $page->section_three_button_two_link : ''); ?>" />
                            </div>

                            <div class="col-md-12 mt-4">
                                <button type="submit" class="btn btn-primary">Update Home</button>
                            </div>

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/admin/cms/home.blade.php ENDPATH**/ ?>