<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="box_shadow_wrapper">
                    <?php echo $__env->make('dashboard.admin.cms.include.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
            </div>

            <form action="#!" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="form-card frst-step">
                    <div class="container">
                        <div class="row my-5">
                            <div class="col-md-12 mb-5">
                                <h3 class="section-heading mb-4"
                                    style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">Privacy
                                    Policy</h3>
                            </div>

                            <input type="hidden" name="type" value="privacy" />

                            <div class="col-md-12 mb-4">
                                <label for="title" class="fieldlabels w-100">Title</label>
                                <input type="text" class="form-control" name="title"
                                    placeholder="Enter privacy policy title" value="<?php echo e($page->title ?? ''); ?>" />
                            </div>


                            <div class="col-md-12 mb-4">
                                <label for="description" class="fieldlabels w-100">Content</label>
                                <textarea class="form-control" name="description" rows="10" placeholder="Enter privacy policy content"><?php echo e($page->description ?? ''); ?></textarea>
                            </div>


                            <div class="col-md-12 mt-4">
                                <button type="submit" class="btn btn-primary">Update</button>
                            </div>

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/admin/cms/privacy.blade.php ENDPATH**/ ?>