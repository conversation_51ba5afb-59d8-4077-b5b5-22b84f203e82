@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="box_shadow_wrapper">
                    @include('dashboard.admin.cms.include.navbar')
                </div>
            </div>

            <form action="#!" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="form-card frst-step">
                    <div class="container">
                        <div class="row my-5">
                            <div class="col-md-12 mb-5">
                                <h3 class="section-heading mb-4"
                                    style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">Privacy
                                    Policy</h3>
                            </div>
                            <input type="hidden" name="type" value="privacy" />

                            <!-- Section 1 -->
                            <div class="col-md-12 mb-5">
                                <div class="d-flex justify-content-end mb-3">
                                    <button type="button" class="btn btn-success btn-sm">Add Block</button>
                                </div>
                                @foreach ($policies as $index => $policy)
                                    <div class="section-wrapper p-4"
                                        style="border: 1px solid #ddd; border-radius: 8px; background-color: #f9f9f9;">
                                        <div class="d-flex justify-content-between align-items-center mb-4">
                                            <h4 class="mb-0" style="color: #555; font-weight: 600;">Section # {{ $index + 1 }}</h4>
                                            @if (!$loop->first)
                                                <button type="button" class="btn btn-danger btn-sm">Delete this
                                                    block</button>
                                            @endif
                                        </div>

                                        <div class="mb-4">
                                            <label for="title" class="fieldlabels w-100">Title</label>
                                            <input type="text" class="form-control"
                                                name="privacy[{{ $index }}][title]"
                                                placeholder="Enter privacy policy title" value="{{ $page->title ?? '' }}" />
                                        </div>

                                        <div class="mb-4">
                                            <label for="description" class="fieldlabels w-100">Content</label>
                                            <textarea class="form-control" name="privacy[{{ $index }}][description]" rows="10"
                                                placeholder="Enter privacy policy content">{{ $page->description ?? '' }}</textarea>
                                        </div>
                                    </div>
                                @endforeach

                            </div>

                            <div class="col-md-12 mt-4">
                                <button type="submit" class="btn btn-primary">Update</button>
                            </div>

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
