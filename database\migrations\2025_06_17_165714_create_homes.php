<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('homes', function (Blueprint $table) {
            $table->id();
            $table->string('banner_image')->nullable();
            $table->string('kicker')->nullable();
            $table->string('heading_one')->nullable();
            $table->string('heading_two')->nullable();
            $table->string('description')->nullable();
            $table->string('image')->nullable();
            $table->string('section_two_title')->nullable();
            $table->string('section_two_image')->nullable();
            $table->string('section_two_heading')->nullable();
            $table->string('section_two_description')->nullable();
            $table->string('section_three_title')->nullable();
            $table->string('section_three_bg_image')->nullable();
            $table->string('section_three_heading')->nullable();
            $table->string('section_three_button_one_text')->nullable();
            $table->string('section_three_button_one_link')->nullable();
            $table->string('section_three_button_two_text')->nullable();
            $table->string('section_three_button_two_link')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('homes');
    }
};
