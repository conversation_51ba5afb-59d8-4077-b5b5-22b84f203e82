<?php

namespace App\Http\Controllers;

use App\Models\Home;
use App\Models\PrivacyAndTerm;
use Illuminate\Http\Request;

class CmsController extends Controller
{
    public function home()
    {
        $page = Home::first();
        return view('dashboard.admin.cms.home',compact('page'));
    }

    public function privacy()
    {
        $policies = PrivacyAndTerm::where('type','privacy')->get();
        return view('dashboard.admin.cms.privacy',compact('policies'));
    }

    public function terms()
    {
        $page = PrivacyAndTerm::where('type','term')->get();
        return view('dashboard.admin.cms.terms',compact('page'));
    }


}
