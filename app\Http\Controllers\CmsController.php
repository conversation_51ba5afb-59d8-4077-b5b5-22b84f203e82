<?php

namespace App\Http\Controllers;

use App\Models\Home;
use App\Models\HomeDetail;
use App\Models\PrivacyAndTerm;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CmsController extends Controller
{
    public function home()
    {
        $page = Home::with('details')->first();
        return view('dashboard.admin.cms.home',compact('page'));
    }

    public function privacy()
    {
        $policies = PrivacyAndTerm::where('type','privacy')->get();
        return view('dashboard.admin.cms.privacy',compact('policies'));
    }

    public function terms()
    {
        $terms = PrivacyAndTerm::where('type','term')->get();
        return view('dashboard.admin.cms.terms',compact('terms'));
    }

    public function storeHome(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'kicker' => 'nullable|string|max:255',
            'heading_one' => 'nullable|string|max:255',
            'heading_two' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'section_two_title' => 'nullable|string|max:255',
            'section_two_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'section_two_heading' => 'nullable|string|max:255',
            'section_two_description' => 'nullable|string',
            'section_three_title' => 'nullable|string|max:255',
            'section_three_bg_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'section_three_heading' => 'nullable|string|max:255',
            'section_three_button_one_text' => 'nullable|string|max:255',
            'section_three_button_one_link' => 'nullable|url',
            'section_three_button_two_text' => 'nullable|string|max:255',
            'section_three_button_two_link' => 'nullable|url',
            'details' => 'nullable|array',
            'details.*.title' => 'nullable|string|max:255',
            'details.*.description' => 'nullable|string',
            'details.*.image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $home = Home::first();
            if (!$home) {
                $home = new Home();
            }
            $homeData = $validator->validated();
            if ($request->hasFile('banner_image')) {
                if ($home->banner_image) {
                    $this->deleteImage($home->banner_image);
                }
                $homeData['banner_image'] = $this->storeImage('home-images', $request->file('banner_image'));
            }
            if ($request->hasFile('image')) {
                if ($home->image) {
                    $this->deleteImage($home->image);
                }
                $homeData['image'] = $this->storeImage('home-images', $request->file('image'));
            }
            if ($request->hasFile('section_two_image')) {
                if ($home->section_two_image) {
                    $this->deleteImage($home->section_two_image);
                }
                $homeData['section_two_image'] = $this->storeImage('home-images', $request->file('section_two_image'));
            }
            if ($request->hasFile('section_three_bg_image')) {
                if ($home->section_three_bg_image) {
                    $this->deleteImage($home->section_three_bg_image);
                }
                $homeData['section_three_bg_image'] = $this->storeImage('home-images', $request->file('section_three_bg_image'));
            }
            unset($homeData['details']);
            $home->fill($homeData);
            $home->save();

            if ($request->has('details')) {
                $home->details()->delete();

                foreach ($request->details as $index => $detailData) {
                    if (!empty($detailData['title']) || !empty($detailData['description'])) {
                        $detail = new HomeDetail();
                        $detail->home_id = $home->id;
                        $detail->title = $detailData['title'] ?? null;
                        $detail->description = $detailData['description'] ?? null;
                        if ($request->hasFile("details.{$index}.image")) {
                            $detail->image = $this->storeImage('home-details', $request->file("details.{$index}.image"));
                        }
                        $detail->save();
                    }
                }
            }
            DB::commit();
            return redirect()->back()->with([
                "type" => "success",
                "title" => "Updated",
                "message" => 'Home page updated successfully!'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with([
                "type" => "error",
                "message" => $th->getMessage(),
                "title" => "Error"
            ]);
        }
    }

    public function storePrivacyTerms(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:privacy,term',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'privacy' => 'nullable|array',
            'privacy.*.title' => 'nullable|string|max:255',
            'privacy.*.description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }

        try {
            DB::beginTransaction();

            $type = $request->input('type');
            $pageType = $type === 'privacy' ? 'term';
            if ($request->has('privacy') && is_array($request->privacy)) {
                PrivacyAndTerm::where('type', $type)->delete();
                foreach ($request->privacy as $sectionData) {
                    if (!empty($sectionData['title']) || !empty($sectionData['description'])) {
                        PrivacyAndTerm::create([
                            'type' => $type,
                            'title' => $sectionData['title'] ?? null,
                            'description' => $sectionData['description'] ?? null,
                        ]);
                    }
                }
            } else {
                $page = PrivacyAndTerm::where('type', $type)->first();
                if (!$page) {
                    $page = new PrivacyAndTerm();
                    $page->type = $type;
                }
                $page->title = $request->input('title');
                $page->description = $request->input('description');
                $page->save();
            }
            DB::commit();
            return redirect()->back()->with([
                "type" => "success",
                "title" => "Updated",
                "message" => $pageType . ' updated successfully!'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with([
                "type" => "error",
                "message" => $th->getMessage(),
                "title" => "Error"
            ]);
        }
    }

}
