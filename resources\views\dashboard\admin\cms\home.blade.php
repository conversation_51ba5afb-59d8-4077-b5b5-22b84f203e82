@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="box_shadow_wrapper">
                    @include('dashboard.admin.cms.include.navbar')
                </div>
            </div>

            <form action="#!" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="form-card frst-step">
                    <div class="container">
                        <div class="row my-5">
                            <!-- Main Home Heading -->
                            <div class="col-md-12 mb-5">
                                <h2 class="main-heading mb-4" style="color: #333; border-bottom: 3px solid #000; padding-bottom: 15px; font-weight: bold;">Home</h2>
                            </div>

                            <!-- All Sub-sections Container -->
                            <div class="col-md-12">
                                <div class="sub-sections-container">

                                    <!-- Hero Section -->
                                    <div class="section-block mb-5 p-4" style="border: 1px solid #e0e0e0; border-radius: 10px; background-color: #fafafa;">
                                        <h4 class="sub-heading mb-4" style="color: #555; border-bottom: 2px solid #007bff; padding-bottom: 10px;">Hero Section</h4>

                            <div class="col-md-12 mb-4">
                                <label for="banner_image" class="fieldlabels w-100">Banner Image</label>
                                <input type="file" class="form-control" name="banner_image" accept="image/*" />
                                @if ($page && $page->banner_image)
                                    <small class="text-muted">Current: {{ $page->banner_image ?? '' }}</small>
                                @endif
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="kicker" class="fieldlabels w-100">Kicker</label>
                                <input type="text" class="form-control" name="kicker" placeholder="Enter kicker text"
                                    value="{{ $page->kicker ?? '' }}" />
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="heading_one" class="fieldlabels w-100">Heading One</label>
                                <input type="text" class="form-control" name="heading_one"
                                    placeholder="Enter first heading" value="{{ $page->heading_one ?? '' }}" />
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="heading_two" class="fieldlabels w-100">Heading Two</label>
                                <input type="text" class="form-control" name="heading_two"
                                    placeholder="Enter second heading" value="{{ $page->heading_two ?? '' }}" />
                            </div>

                            <div class="col-md-12 mb-4">
                                <label for="description" class="fieldlabels w-100">Description</label>
                                <textarea class="form-control" name="description" rows="4" placeholder="Enter description">{{ $page->description ?? '' }}</textarea>
                            </div>

                                        <div class="col-md-12 mb-4">
                                            <label for="image" class="fieldlabels w-100">Image</label>
                                            <input type="file" class="form-control" name="image" accept="image/*" />
                                            @if ($page && $page->image)
                                                <small class="text-muted">Current: {{ $page->image ?? '' }}</small>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Section Two -->
                                    <div class="section-block mb-5 p-4" style="border: 1px solid #e0e0e0; border-radius: 10px; background-color: #fafafa;">
                                        <h4 class="sub-heading mb-4" style="color: #555; border-bottom: 2px solid #28a745; padding-bottom: 10px;">Section Two</h4>

                                        <div class="col-md-12 mb-4">
                                            <label for="section_two_title" class="fieldlabels w-100">Section Two Title</label>
                                            <input type="text" class="form-control" name="section_two_title"
                                                placeholder="Enter section two title" value="{{ $page->section_two_title ?? '' }}" />
                                        </div>

                                        <div class="col-md-12 mb-4">
                                            <label for="section_two_image" class="fieldlabels w-100">Section Two Image</label>
                                            <input type="file" class="form-control" name="section_two_image" accept="image/*" />
                                            @if ($page && $page->section_two_image)
                                                <small class="text-muted">Current: {{ $page->section_two_image ?? '' }}</small>
                                            @endif
                                        </div>

                                        <div class="col-md-12 mb-4">
                                            <label for="section_two_heading" class="fieldlabels w-100">Section Two Heading</label>
                                            <input type="text" class="form-control" name="section_two_heading"
                                                placeholder="Enter section two heading"
                                                value="{{ $page->section_two_heading ?? '' }}" />
                                        </div>

                                        <div class="col-md-12 mb-4">
                                            <label for="section_two_description" class="fieldlabels w-100">Section Two
                                                Description</label>
                                            <textarea class="form-control" name="section_two_description" rows="4"
                                                placeholder="Enter section two description">{{ $page->section_two_description ?? '' }}</textarea>
                                        </div>
                                    </div>

                                    <!-- Dynamic Sub-sections -->
                                    <div class="section-block mb-5 p-4" style="border: 1px solid #e0e0e0; border-radius: 10px; background-color: #fafafa;">
                                        <h4 class="sub-heading mb-4" style="color: #555; border-bottom: 2px solid #ffc107; padding-bottom: 10px;">Dynamic Sub-sections</h4>

                                        <div class="d-flex justify-content-end mb-3">
                                            <button type="button" class="btn btn-success btn-sm add-btn">Add Block</button>
                                        </div>

                                        @foreach ($page->details as $index => $detail)
                                            <div class="section-wrapper p-4 mb-4"
                                                style="border: 1px solid #ddd; border-radius: 8px; background-color: #f9f9f9;">
                                                <div class="d-flex justify-content-between align-items-center mb-4">
                                                    <h5 class="mb-0" style="color: #555; font-weight: 600;">Sub-section #
                                                        {{ $index + 1 }}</h5>
                                                    @if (!$loop->first)
                                                        <button type="button"
                                                            class="btn btn-danger btn-sm delete-block-btn">Delete this
                                                            block</button>
                                                    @endif
                                                </div>

                                                <div class="col-md-12 mb-4">
                                                    <label for="banner_image" class="fieldlabels w-100">Banner Image</label>
                                                    <input type="file" class="form-control"
                                                        name="details[{{ $index }}][image" accept="image/*" />
                                                    @if ($detail && $detail->banner_image)
                                                        <small class="text-muted">Current:
                                                            {{ $detail->banner_image ?? '' }}</small>
                                                    @endif
                                                </div>

                                                <div class="mb-4">
                                                    <label for="title" class="fieldlabels w-100">Title</label>
                                                    <input type="text" class="form-control"
                                                        name="details[{{ $index }}][title]" placeholder="Enter title"
                                                        value="{{ $detail->title ?? '' }}" />
                                                </div>

                                                <div class="mb-4">
                                                    <label for="description" class="fieldlabels w-100">Content</label>
                                                    <textarea class="form-control" name="details[{{ $index }}][description]" rows="10"
                                                        placeholder="Enter content">{{ $detail->description ?? '' }}</textarea>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>

                                    <!-- Section Three -->
                                    <div class="section-block mb-5 p-4" style="border: 1px solid #e0e0e0; border-radius: 10px; background-color: #fafafa;">
                                        <h4 class="sub-heading mb-4" style="color: #555; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">Section Three</h4>

                                        <div class="col-md-12 mb-4">
                                            <label for="section_three_title" class="fieldlabels w-100">Section Three Title</label>
                                            <input type="text" class="form-control" name="section_three_title"
                                                placeholder="Enter section three title"
                                                value="{{ $page->section_three_title ?? '' }}" />
                                        </div>

                                        <div class="col-md-12 mb-4">
                                            <label for="section_three_bg_image" class="fieldlabels w-100">Section Three Background
                                                Image</label>
                                            <input type="file" class="form-control" name="section_three_bg_image"
                                                accept="image/*" />
                                            @if ($page && $page->section_three_bg_image)
                                                <small class="text-muted">Current: {{ $page->section_three_bg_image ?? '' }}</small>
                                            @endif
                                        </div>

                                        <div class="col-md-12 mb-4">
                                            <label for="section_three_heading" class="fieldlabels w-100">Section Three Heading</label>
                                            <input type="text" class="form-control" name="section_three_heading"
                                                placeholder="Enter section three heading"
                                                value="{{ $page->section_three_heading ?? '' }}" />
                                        </div>

                                        <div class="col-md-6 mb-4">
                                            <label for="section_three_button_one_text" class="fieldlabels w-100">Button One
                                                Text</label>
                                            <input type="text" class="form-control" name="section_three_button_one_text"
                                                placeholder="Enter button one text"
                                                value="{{ $page->section_three_button_one_text ?? '' }}" />
                                        </div>

                                        <div class="col-md-6 mb-4">
                                            <label for="section_three_button_one_link" class="fieldlabels w-100">Button One
                                                Link</label>
                                            <input type="url" class="form-control" name="section_three_button_one_link"
                                                placeholder="Enter button one link"
                                                value="{{ $page->section_three_button_one_link ?? '' }}" />
                                        </div>

                                        <div class="col-md-6 mb-4">
                                            <label for="section_three_button_two_text" class="fieldlabels w-100">Button Two
                                                Text</label>
                                            <input type="text" class="form-control" name="section_three_button_two_text"
                                                placeholder="Enter button two text"
                                                value="{{ $page->section_three_button_two_text ?? '' }}" />
                                        </div>

                                        <div class="col-md-6 mb-4">
                                            <label for="section_three_button_two_link" class="fieldlabels w-100">Button Two
                                                Link</label>
                                            <input type="url" class="form-control" name="section_three_button_two_link"
                                                placeholder="Enter button two link"
                                                value="{{ $page->section_three_button_two_link ?? '' }}" />
                                        </div>
                                    </div>

                                </div> <!-- End sub-sections-container -->
                            </div> <!-- End col-md-12 -->

                            <!-- Submit Button -->
                            <div class="col-md-12 mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">Update Home</button>
                            </div>

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
@push('js')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $(document).on('click', '.add-btn', function() {
                const section = $('.section-wrapper').length;
                const newSection = `
                    <div class="section-wrapper p-4 mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4 class="mb-0" style="color: #555; font-weight: 600;">Sub-section # ${section+1}</h4>
                            <button type="button" class="btn btn-danger btn-sm delete-block-btn">Delete this block</button>
                        </div>

                        <div class="col-md-12 mb-4">
                            <label for="banner_image" class="fieldlabels w-100">Banner Image</label>
                            <input type="file" class="form-control" name="details[${section}][image]" accept="image/*" />
                        </div>

                        <div class="mb-4">
                            <label for="title" class="fieldlabels w-100">Title</label>
                            <input type="text" class="form-control" name="details[${section}][title]" placeholder="Enter title" />
                        </div>

                        <div class="mb-4">
                            <label for="description" class="fieldlabels w-100">Content</label>
                            <textarea class="form-control" name="details[${section}][description]" rows="10" placeholder="Enter content"></textarea>
                        </div>
                    </div>
                `;

                $('.section-wrapper:last').after(newSection);
            });

            // Delete Block functionality
            $(document).on('click', '.delete-block-btn', function() {
                const totalSections = $('.section-wrapper').length;

                if (totalSections > 1) {
                    $(this).closest('.section-wrapper').remove();
                    updateSectionNumbers();
                } else {
                    alert('You must have at least one section.');
                }
            });

            // Update section numbers after deletion
            function updateSectionNumbers() {
                $('.section-wrapper').each(function(index) {
                    const sectionNumber = index + 1;
                    $(this).find('h4').text('Sub-section # ' + sectionNumber);
                });
            }
        });
    </script>
@endpush